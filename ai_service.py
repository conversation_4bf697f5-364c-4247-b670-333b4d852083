# AI服务模块 - 处理DeepSeek AI调用和图片分析
import openai
import cv2
import numpy as np
from PIL import Image
import base64
import io
from typing import Dict, List, Any
import config

class AIService:
    def __init__(self):
        """初始化AI服务"""
        self.client = openai.OpenAI(
            api_key=config.DEEPSEEK_API_KEY,
            base_url=config.DEEPSEEK_BASE_URL
        )
    
    def analyze_image(self, image: Image.Image) -> Dict[str, Any]:
        """分析图片内容，提取关键信息"""
        try:
            # 将PIL图片转换为OpenCV格式
            opencv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
            
            # 基本图片分析
            height, width = opencv_image.shape[:2]
            
            # 颜色分析
            dominant_colors = self._get_dominant_colors(opencv_image)
            
            # 亮度分析
            brightness = self._calculate_brightness(opencv_image)
            
            # 将图片转换为base64用于AI分析
            image_base64 = self._image_to_base64(image)
            
            # 调用AI进行内容分析
            ai_analysis = self._analyze_image_with_ai(image_base64)
            
            return {
                "dimensions": {"width": width, "height": height},
                "dominant_colors": dominant_colors,
                "brightness": brightness,
                "ai_analysis": ai_analysis,
                "layout_suggestion": self._suggest_layout_for_image(ai_analysis, brightness)
            }
        except Exception as e:
            return {"error": f"图片分析失败: {str(e)}"}
    
    def generate_content_from_text(self, text_description: str) -> Dict[str, Any]:
        """根据文字描述生成PPT内容"""
        try:
            prompt = f"""
            基于以下描述，为单页PPT生成内容结构：
            
            描述：{text_description}
            
            请生成：
            1. 一个简洁有力的标题（不超过15个字）
            2. 3-5个要点内容（每个要点不超过20个字）
            3. 推荐的布局类型（title_content, title_image_content, full_image）
            4. 推荐的配色方案
            
            请以JSON格式返回结果。
            """
            
            response = self.client.chat.completions.create(
                model="deepseek-chat",
                messages=[
                    {"role": "system", "content": "你是一个专业的PPT设计助手，擅长根据内容生成简洁美观的PPT布局。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.7
            )
            
            content = response.choices[0].message.content
            return self._parse_ai_response(content)
            
        except Exception as e:
            return {"error": f"内容生成失败: {str(e)}"}
    
    def _get_dominant_colors(self, image: np.ndarray, k: int = 3) -> List[List[int]]:
        """获取图片主要颜色"""
        try:
            # 重塑图片数据
            data = image.reshape((-1, 3))
            data = np.float32(data)
            
            # 使用K-means聚类找到主要颜色
            criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 20, 1.0)
            _, labels, centers = cv2.kmeans(data, k, None, criteria, 10, cv2.KMEANS_RANDOM_CENTERS)
            
            # 转换为RGB格式并返回
            centers = np.uint8(centers)
            dominant_colors = []
            for center in centers:
                # BGR转RGB
                rgb_color = [int(center[2]), int(center[1]), int(center[0])]
                dominant_colors.append(rgb_color)
            
            return dominant_colors
        except:
            return [[255, 255, 255], [0, 0, 0], [128, 128, 128]]
    
    def _calculate_brightness(self, image: np.ndarray) -> float:
        """计算图片亮度"""
        try:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            return float(np.mean(gray)) / 255.0
        except:
            return 0.5
    
    def _image_to_base64(self, image: Image.Image) -> str:
        """将PIL图片转换为base64字符串"""
        try:
            buffer = io.BytesIO()
            image.save(buffer, format='PNG')
            img_str = base64.b64encode(buffer.getvalue()).decode()
            return img_str
        except:
            return ""
    
    def _analyze_image_with_ai(self, image_base64: str) -> Dict[str, Any]:
        """使用AI分析图片内容"""
        try:
            prompt = """
            请分析这张图片并提供以下信息：
            1. 主要内容描述
            2. 图片风格（商务、学术、创意、个人等）
            3. 适合的PPT主题
            4. 建议的文字位置
            
            请以JSON格式返回结果。
            """
            
            # 注意：这里简化了实现，实际使用时需要根据DeepSeek API的具体要求调整
            response = self.client.chat.completions.create(
                model="deepseek-chat",
                messages=[
                    {"role": "system", "content": "你是一个专业的图片分析师，擅长分析图片内容并提供PPT设计建议。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3
            )
            
            content = response.choices[0].message.content
            return self._parse_ai_response(content)
            
        except Exception as e:
            return {
                "description": "图片分析",
                "style": "通用",
                "theme": "基于图片内容",
                "text_position": "右侧"
            }
    
    def _suggest_layout_for_image(self, ai_analysis: Dict, brightness: float) -> str:
        """根据图片分析结果建议布局"""
        if brightness > 0.7:
            return "title_image_content"  # 亮图片适合文字叠加
        elif brightness < 0.3:
            return "title_content"  # 暗图片可能不适合作为背景
        else:
            return "title_image_content"  # 中等亮度适合图文并茂
    
    def _parse_ai_response(self, content: str) -> Dict[str, Any]:
        """解析AI返回的内容"""
        try:
            import json
            # 尝试解析JSON
            if content.strip().startswith('{'):
                return json.loads(content)
            else:
                # 如果不是JSON格式，返回默认结构
                return {
                    "title": "AI生成标题",
                    "content": content.split('\n')[:5],
                    "layout": "title_content",
                    "colors": ["#2E3440", "#FFFFFF", "#5E81AC"]
                }
        except:
            return {
                "title": "AI生成内容",
                "content": ["要点1", "要点2", "要点3"],
                "layout": "title_content",
                "colors": ["#2E3440", "#FFFFFF", "#5E81AC"]
            }
