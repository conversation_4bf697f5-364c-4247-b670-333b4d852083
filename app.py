# AI单页PPT生成器 - Streamlit主应用
import streamlit as st
import tempfile
import os
from PIL import Image
import io
from ai_service import AIService
from ppt_generator import PPTGenerator
import config

# 页面配置
st.set_page_config(
    page_title="AI单页PPT生成器",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS样式
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        color: #2E3440;
        text-align: center;
        margin-bottom: 2rem;
        font-weight: bold;
    }
    .sub-header {
        font-size: 1.5rem;
        color: #5E81AC;
        margin-bottom: 1rem;
    }
    .info-box {
        background-color: #ECEFF4;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #5E81AC;
        margin: 1rem 0;
    }
    .success-box {
        background-color: #D8DEE9;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #A3BE8C;
        margin: 1rem 0;
    }
    .stButton > button {
        background-color: #5E81AC;
        color: white;
        border-radius: 0.5rem;
        border: none;
        padding: 0.5rem 1rem;
        font-weight: bold;
    }
    .stButton > button:hover {
        background-color: #81A1C1;
    }
</style>
""", unsafe_allow_html=True)

def initialize_session_state():
    """初始化会话状态"""
    if 'ai_service' not in st.session_state:
        st.session_state.ai_service = AIService()
    if 'ppt_generator' not in st.session_state:
        st.session_state.ppt_generator = PPTGenerator()
    if 'generated_ppt' not in st.session_state:
        st.session_state.generated_ppt = None
    if 'ppt_bytes' not in st.session_state:
        st.session_state.ppt_bytes = None

def main():
    """主函数"""
    initialize_session_state()
    
    # 主标题
    st.markdown('<h1 class="main-header">🎯 AI单页PPT生成器</h1>', unsafe_allow_html=True)
    
    # 项目介绍
    with st.expander("📖 项目介绍", expanded=False):
        st.markdown("""
        **AI单页PPT生成器** 是一个基于人工智能的在线工具，能够根据您提供的图片或文字描述，
        快速生成专业、美观的单页PPT布局。
        
        **主要功能：**
        - 📷 **图片上传分析**：上传图片，AI自动分析内容并生成相应布局
        - ✍️ **文字描述生成**：输入文字描述，AI智能生成PPT内容和布局
        - 🎨 **智能布局设计**：基于内容自动选择最佳布局方案
        - 📥 **一键导出**：生成的PPT可直接下载使用
        
        **技术支持：** DeepSeek AI + Langgraph + Python + Streamlit
        """)
    
    # 侧边栏配置
    with st.sidebar:
        st.markdown('<h2 class="sub-header">⚙️ 配置选项</h2>', unsafe_allow_html=True)
        
        # API配置检查
        if config.DEEPSEEK_API_KEY == "your_deepseek_api_key_here":
            st.warning("⚠️ 请在config.py中配置您的DeepSeek API密钥")
        else:
            st.success("✅ API配置已就绪")
        
        st.markdown("---")
        
        # 模板选择
        st.markdown("**🎨 PPT模板风格**")
        template_style = st.selectbox(
            "选择模板风格",
            ["商务风格", "学术风格", "创意风格", "简约风格"],
            index=0
        )
        
        # 颜色主题
        st.markdown("**🌈 颜色主题**")
        color_theme = st.selectbox(
            "选择颜色主题",
            ["经典蓝", "商务灰", "活力橙", "自然绿"],
            index=0
        )
    
    # 主要内容区域
    tab1, tab2 = st.tabs(["📷 图片生成PPT", "✍️ 文字生成PPT"])
    
    with tab1:
        handle_image_input()
    
    with tab2:
        handle_text_input()
    
    # 显示生成结果
    if st.session_state.generated_ppt:
        display_results()

def handle_image_input():
    """处理图片输入"""
    st.markdown('<h2 class="sub-header">📷 上传图片生成PPT</h2>', unsafe_allow_html=True)
    
    # 图片上传
    uploaded_file = st.file_uploader(
        "选择图片文件",
        type=config.SUPPORTED_IMAGE_FORMATS,
        help="支持JPG、PNG、BMP、GIF格式的图片"
    )
    
    if uploaded_file is not None:
        # 显示上传的图片
        col1, col2 = st.columns([1, 1])
        
        with col1:
            st.markdown("**📸 上传的图片**")
            image = Image.open(uploaded_file)
            st.image(image, caption="上传的图片", use_column_width=True)
        
        with col2:
            st.markdown("**📝 补充信息（可选）**")
            
            # 可选的标题和内容输入
            custom_title = st.text_input("自定义标题", placeholder="留空则由AI自动生成")
            custom_content = st.text_area(
                "补充内容要点", 
                placeholder="每行一个要点，留空则由AI自动生成",
                height=100
            )
            
            # 生成按钮
            if st.button("🚀 生成PPT", key="generate_from_image"):
                generate_ppt_from_image(image, custom_title, custom_content)

def handle_text_input():
    """处理文字输入"""
    st.markdown('<h2 class="sub-header">✍️ 文字描述生成PPT</h2>', unsafe_allow_html=True)
    
    # 文字描述输入
    text_description = st.text_area(
        "请输入您的PPT内容描述",
        placeholder="例如：介绍我们公司的新产品发布会，重点突出产品的创新功能和市场优势...",
        height=150,
        help="详细描述您想要展示的内容，AI将根据描述生成相应的PPT布局和内容"
    )
    
    # 示例按钮
    col1, col2, col3 = st.columns(3)
    with col1:
        if st.button("💼 商务示例"):
            st.session_state.text_example = "介绍公司Q4季度业绩报告，重点展示销售增长30%，新客户获取率提升25%，以及明年的战略规划目标。"
    with col2:
        if st.button("🎓 学术示例"):
            st.session_state.text_example = "展示人工智能在医疗诊断中的应用研究，包括深度学习算法的准确率提升，临床试验结果，以及未来发展前景。"
    with col3:
        if st.button("🎨 创意示例"):
            st.session_state.text_example = "展示我们的创意设计工作室，突出独特的设计理念，成功案例展示，以及为客户提供的一站式设计服务。"
    
    # 如果有示例文本，显示在输入框中
    if 'text_example' in st.session_state:
        text_description = st.text_area(
            "请输入您的PPT内容描述",
            value=st.session_state.text_example,
            height=150,
            key="text_with_example"
        )
        del st.session_state.text_example
    
    # 生成按钮
    if st.button("🚀 生成PPT", key="generate_from_text"):
        if text_description.strip():
            generate_ppt_from_text(text_description)
        else:
            st.error("请输入内容描述")

def generate_ppt_from_image(image, custom_title, custom_content):
    """从图片生成PPT"""
    with st.spinner("🔍 正在分析图片..."):
        try:
            # 分析图片
            image_analysis = st.session_state.ai_service.analyze_image(image)
            
            if "error" in image_analysis:
                st.error(f"图片分析失败：{image_analysis['error']}")
                return
            
            # 处理自定义内容
            content_list = []
            if custom_content.strip():
                content_list = [line.strip() for line in custom_content.split('\n') if line.strip()]
            
            # 生成PPT
            st.session_state.ppt_generator = PPTGenerator()
            presentation = st.session_state.ppt_generator.generate_ppt_from_image(
                image, image_analysis, custom_title, content_list
            )
            
            # 获取PPT字节数据
            ppt_bytes = st.session_state.ppt_generator.get_presentation_bytes()
            
            st.session_state.generated_ppt = presentation
            st.session_state.ppt_bytes = ppt_bytes
            st.session_state.generation_type = "image"
            st.session_state.image_analysis = image_analysis
            
            st.success("✅ PPT生成成功！")
            
        except Exception as e:
            st.error(f"生成失败：{str(e)}")

def generate_ppt_from_text(text_description):
    """从文字描述生成PPT"""
    with st.spinner("🤖 正在生成内容..."):
        try:
            # 生成内容
            content_data = st.session_state.ai_service.generate_content_from_text(text_description)
            
            if "error" in content_data:
                st.error(f"内容生成失败：{content_data['error']}")
                return
            
            # 生成PPT
            st.session_state.ppt_generator = PPTGenerator()
            presentation = st.session_state.ppt_generator.generate_ppt_from_text(content_data)
            
            # 获取PPT字节数据
            ppt_bytes = st.session_state.ppt_generator.get_presentation_bytes()
            
            st.session_state.generated_ppt = presentation
            st.session_state.ppt_bytes = ppt_bytes
            st.session_state.generation_type = "text"
            st.session_state.content_data = content_data
            
            st.success("✅ PPT生成成功！")
            
        except Exception as e:
            st.error(f"生成失败：{str(e)}")

def display_results():
    """显示生成结果"""
    st.markdown("---")
    st.markdown('<h2 class="sub-header">🎉 生成结果</h2>', unsafe_allow_html=True)
    
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.markdown('<div class="success-box">✅ PPT已成功生成！您可以预览内容并下载文件。</div>', 
                   unsafe_allow_html=True)
        
        # 显示生成信息
        if st.session_state.generation_type == "image":
            st.markdown("**📊 图片分析结果：**")
            analysis = st.session_state.image_analysis
            if "ai_analysis" in analysis:
                ai_info = analysis["ai_analysis"]
                st.write(f"- 内容描述：{ai_info.get('description', '未知')}")
                st.write(f"- 建议风格：{ai_info.get('style', '通用')}")
                st.write(f"- 推荐主题：{ai_info.get('theme', '基于图片内容')}")
        
        elif st.session_state.generation_type == "text":
            st.markdown("**📝 生成的内容结构：**")
            content = st.session_state.content_data
            st.write(f"- 标题：{content.get('title', '未生成')}")
            st.write(f"- 布局类型：{content.get('layout', '标准布局')}")
            if 'content' in content:
                st.write("- 要点内容：")
                for point in content['content'][:3]:  # 只显示前3个要点
                    st.write(f"  • {point}")
    
    with col2:
        # 下载按钮
        if st.session_state.ppt_bytes:
            st.download_button(
                label="📥 下载PPT文件",
                data=st.session_state.ppt_bytes,
                file_name="AI生成的PPT.pptx",
                mime="application/vnd.openxmlformats-officedocument.presentationml.presentation",
                key="download_ppt"
            )
        
        # 重新生成按钮
        if st.button("🔄 重新生成", key="regenerate"):
            st.session_state.generated_ppt = None
            st.session_state.ppt_bytes = None
            st.experimental_rerun()

if __name__ == "__main__":
    main()
