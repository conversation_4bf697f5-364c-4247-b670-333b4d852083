# 配置文件
import os

# DeepSeek API 配置
DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY", "your_deepseek_api_key_here")
DEEPSEEK_BASE_URL = "https://api.deepseek.com/v1"

# PPT 配置
PPT_WIDTH = 1920  # PPT 宽度（像素）
PPT_HEIGHT = 1080  # PPT 高度（像素）

# 支持的图片格式
SUPPORTED_IMAGE_FORMATS = ["jpg", "jpeg", "png", "bmp", "gif"]

# 默认PPT模板配置
DEFAULT_TEMPLATE = {
    "background_color": "#FFFFFF",
    "title_font_size": 44,
    "content_font_size": 24,
    "title_color": "#2E3440",
    "content_color": "#4C566A",
    "accent_color": "#5E81AC"
}

# 布局模板
LAYOUT_TEMPLATES = {
    "title_content": {
        "title": {"x": 0.1, "y": 0.1, "width": 0.8, "height": 0.2},
        "content": {"x": 0.1, "y": 0.35, "width": 0.8, "height": 0.5}
    },
    "title_image_content": {
        "title": {"x": 0.1, "y": 0.05, "width": 0.8, "height": 0.15},
        "image": {"x": 0.1, "y": 0.25, "width": 0.4, "height": 0.6},
        "content": {"x": 0.55, "y": 0.25, "width": 0.35, "height": 0.6}
    },
    "full_image": {
        "image": {"x": 0, "y": 0, "width": 1, "height": 1},
        "title_overlay": {"x": 0.1, "y": 0.1, "width": 0.8, "height": 0.2}
    }
}
