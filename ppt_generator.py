# PPT生成器模块
from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
from pptx.enum.text import PP_ALIGN
from PIL import Image
import io
import tempfile
import os
from typing import Dict, Any, Optional
import config

class PPTGenerator:
    def __init__(self):
        """初始化PPT生成器"""
        self.presentation = None
        self.slide = None
    
    def create_presentation(self) -> Presentation:
        """创建新的PPT演示文稿"""
        self.presentation = Presentation()
        # 设置幻灯片尺寸
        self.presentation.slide_width = Inches(13.33)  # 1920px at 144 DPI
        self.presentation.slide_height = Inches(7.5)   # 1080px at 144 DPI
        return self.presentation
    
    def generate_ppt_from_text(self, content_data: Dict[str, Any]) -> Presentation:
        """根据文字内容生成PPT"""
        try:
            if not self.presentation:
                self.create_presentation()
            
            # 添加空白幻灯片
            blank_slide_layout = self.presentation.slide_layouts[6]  # 空白布局
            self.slide = self.presentation.slides.add_slide(blank_slide_layout)
            
            # 获取内容
            title = content_data.get("title", "默认标题")
            content_points = content_data.get("content", ["默认内容"])
            layout_type = content_data.get("layout", "title_content")
            colors = content_data.get("colors", config.DEFAULT_TEMPLATE)
            
            # 根据布局类型生成PPT
            if layout_type == "title_content":
                self._create_title_content_layout(title, content_points, colors)
            else:
                self._create_title_content_layout(title, content_points, colors)
            
            return self.presentation
            
        except Exception as e:
            raise Exception(f"PPT生成失败: {str(e)}")
    
    def generate_ppt_from_image(self, image: Image.Image, image_analysis: Dict[str, Any], 
                               title: str = "", content: list = None) -> Presentation:
        """根据图片生成PPT"""
        try:
            if not self.presentation:
                self.create_presentation()
            
            # 添加空白幻灯片
            blank_slide_layout = self.presentation.slide_layouts[6]
            self.slide = self.presentation.slides.add_slide(blank_slide_layout)
            
            # 获取布局建议
            layout_type = image_analysis.get("layout_suggestion", "title_image_content")
            ai_analysis = image_analysis.get("ai_analysis", {})
            
            # 如果没有提供标题和内容，使用AI分析结果
            if not title:
                title = ai_analysis.get("theme", "基于图片的演示")
            if not content:
                content = [ai_analysis.get("description", "图片内容描述")]
            
            # 保存图片到临时文件
            temp_image_path = self._save_temp_image(image)
            
            try:
                if layout_type == "title_image_content":
                    self._create_title_image_content_layout(title, content, temp_image_path)
                elif layout_type == "full_image":
                    self._create_full_image_layout(title, temp_image_path)
                else:
                    self._create_title_image_content_layout(title, content, temp_image_path)
            finally:
                # 清理临时文件
                if os.path.exists(temp_image_path):
                    os.remove(temp_image_path)
            
            return self.presentation
            
        except Exception as e:
            raise Exception(f"基于图片的PPT生成失败: {str(e)}")
    
    def _create_title_content_layout(self, title: str, content_points: list, colors: Dict):
        """创建标题+内容布局"""
        layout = config.LAYOUT_TEMPLATES["title_content"]
        
        # 添加标题
        title_box = self.slide.shapes.add_textbox(
            Inches(layout["title"]["x"] * 13.33),
            Inches(layout["title"]["y"] * 7.5),
            Inches(layout["title"]["width"] * 13.33),
            Inches(layout["title"]["height"] * 7.5)
        )
        title_frame = title_box.text_frame
        title_frame.text = title
        title_frame.paragraphs[0].font.size = Pt(config.DEFAULT_TEMPLATE["title_font_size"])
        title_frame.paragraphs[0].font.bold = True
        title_frame.paragraphs[0].alignment = PP_ALIGN.CENTER
        
        # 添加内容
        content_box = self.slide.shapes.add_textbox(
            Inches(layout["content"]["x"] * 13.33),
            Inches(layout["content"]["y"] * 7.5),
            Inches(layout["content"]["width"] * 13.33),
            Inches(layout["content"]["height"] * 7.5)
        )
        content_frame = content_box.text_frame
        
        # 添加内容要点
        for i, point in enumerate(content_points):
            if i == 0:
                p = content_frame.paragraphs[0]
            else:
                p = content_frame.add_paragraph()
            
            p.text = f"• {point}"
            p.font.size = Pt(config.DEFAULT_TEMPLATE["content_font_size"])
            p.space_after = Pt(12)
    
    def _create_title_image_content_layout(self, title: str, content_points: list, image_path: str):
        """创建标题+图片+内容布局"""
        layout = config.LAYOUT_TEMPLATES["title_image_content"]
        
        # 添加标题
        title_box = self.slide.shapes.add_textbox(
            Inches(layout["title"]["x"] * 13.33),
            Inches(layout["title"]["y"] * 7.5),
            Inches(layout["title"]["width"] * 13.33),
            Inches(layout["title"]["height"] * 7.5)
        )
        title_frame = title_box.text_frame
        title_frame.text = title
        title_frame.paragraphs[0].font.size = Pt(config.DEFAULT_TEMPLATE["title_font_size"])
        title_frame.paragraphs[0].font.bold = True
        title_frame.paragraphs[0].alignment = PP_ALIGN.CENTER
        
        # 添加图片
        self.slide.shapes.add_picture(
            image_path,
            Inches(layout["image"]["x"] * 13.33),
            Inches(layout["image"]["y"] * 7.5),
            Inches(layout["image"]["width"] * 13.33),
            Inches(layout["image"]["height"] * 7.5)
        )
        
        # 添加内容
        content_box = self.slide.shapes.add_textbox(
            Inches(layout["content"]["x"] * 13.33),
            Inches(layout["content"]["y"] * 7.5),
            Inches(layout["content"]["width"] * 13.33),
            Inches(layout["content"]["height"] * 7.5)
        )
        content_frame = content_box.text_frame
        
        for i, point in enumerate(content_points):
            if i == 0:
                p = content_frame.paragraphs[0]
            else:
                p = content_frame.add_paragraph()
            
            p.text = f"• {point}"
            p.font.size = Pt(config.DEFAULT_TEMPLATE["content_font_size"])
            p.space_after = Pt(12)
    
    def _create_full_image_layout(self, title: str, image_path: str):
        """创建全图片布局"""
        layout = config.LAYOUT_TEMPLATES["full_image"]
        
        # 添加背景图片
        self.slide.shapes.add_picture(
            image_path,
            Inches(0),
            Inches(0),
            Inches(13.33),
            Inches(7.5)
        )
        
        # 添加标题覆盖层
        title_box = self.slide.shapes.add_textbox(
            Inches(layout["title_overlay"]["x"] * 13.33),
            Inches(layout["title_overlay"]["y"] * 7.5),
            Inches(layout["title_overlay"]["width"] * 13.33),
            Inches(layout["title_overlay"]["height"] * 7.5)
        )
        title_frame = title_box.text_frame
        title_frame.text = title
        title_frame.paragraphs[0].font.size = Pt(config.DEFAULT_TEMPLATE["title_font_size"])
        title_frame.paragraphs[0].font.bold = True
        title_frame.paragraphs[0].font.color.rgb = RGBColor(255, 255, 255)  # 白色文字
        title_frame.paragraphs[0].alignment = PP_ALIGN.CENTER
    
    def _save_temp_image(self, image: Image.Image) -> str:
        """保存图片到临时文件"""
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.png')
        image.save(temp_file.name, 'PNG')
        temp_file.close()
        return temp_file.name
    
    def save_presentation(self, filename: str = "generated_ppt.pptx") -> str:
        """保存PPT文件"""
        try:
            if not self.presentation:
                raise Exception("没有可保存的演示文稿")
            
            # 确保文件名以.pptx结尾
            if not filename.endswith('.pptx'):
                filename += '.pptx'
            
            self.presentation.save(filename)
            return filename
            
        except Exception as e:
            raise Exception(f"保存PPT失败: {str(e)}")
    
    def get_presentation_bytes(self) -> bytes:
        """获取PPT文件的字节数据"""
        try:
            if not self.presentation:
                raise Exception("没有可导出的演示文稿")
            
            # 保存到内存中的字节流
            ppt_bytes = io.BytesIO()
            self.presentation.save(ppt_bytes)
            ppt_bytes.seek(0)
            return ppt_bytes.getvalue()
            
        except Exception as e:
            raise Exception(f"导出PPT失败: {str(e)}")
