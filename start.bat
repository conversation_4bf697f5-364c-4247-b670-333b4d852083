@echo off
echo ========================================
echo    AI单页PPT生成器 启动脚本
echo ========================================
echo.

echo 正在检查Python环境...
python --version
if %errorlevel% neq 0 (
    echo 错误：未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

echo.
echo 正在检查依赖包...
pip show streamlit >nul 2>&1
if %errorlevel% neq 0 (
    echo 正在安装依赖包...
    pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo 错误：依赖包安装失败
        pause
        exit /b 1
    )
)

echo.
echo 正在启动应用...
echo 浏览器将自动打开 http://localhost:8501
echo 如果没有自动打开，请手动访问上述地址
echo.
echo 按 Ctrl+C 停止应用
echo ========================================

streamlit run app.py

pause
