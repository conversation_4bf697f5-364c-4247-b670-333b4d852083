#!/bin/bash

echo "========================================"
echo "    AI单页PPT生成器 启动脚本"
echo "========================================"
echo

echo "正在检查Python环境..."
if ! command -v python3 &> /dev/null; then
    echo "错误：未找到Python3，请先安装Python 3.8+"
    exit 1
fi

python3 --version

echo
echo "正在检查依赖包..."
if ! python3 -c "import streamlit" &> /dev/null; then
    echo "正在安装依赖包..."
    pip3 install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "错误：依赖包安装失败"
        exit 1
    fi
fi

echo
echo "正在启动应用..."
echo "浏览器将自动打开 http://localhost:8501"
echo "如果没有自动打开，请手动访问上述地址"
echo
echo "按 Ctrl+C 停止应用"
echo "========================================"

streamlit run app.py
