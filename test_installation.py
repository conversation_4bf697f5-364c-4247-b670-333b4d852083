#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装测试脚本 - 检查所有依赖是否正确安装
"""

import sys
import importlib

def test_import(module_name, package_name=None):
    """测试模块导入"""
    try:
        importlib.import_module(module_name)
        print(f"✅ {package_name or module_name} - 导入成功")
        return True
    except ImportError as e:
        print(f"❌ {package_name or module_name} - 导入失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("AI单页PPT生成器 - 依赖检查")
    print("=" * 50)
    
    # 检查Python版本
    print(f"\nPython版本: {sys.version}")
    if sys.version_info < (3, 8):
        print("❌ Python版本过低，需要3.8或更高版本")
        return False
    else:
        print("✅ Python版本符合要求")
    
    print("\n检查依赖包:")
    print("-" * 30)
    
    # 要检查的包列表
    packages = [
        ("streamlit", "Streamlit"),
        ("pptx", "python-pptx"),
        ("PIL", "Pillow"),
        ("cv2", "opencv-python"),
        ("pandas", "pandas"),
        ("matplotlib", "matplotlib"),
        ("plotly", "plotly"),
        ("openai", "openai"),
        ("requests", "requests"),
        ("numpy", "numpy"),
        ("langchain", "langchain"),
        ("langgraph", "langgraph")
    ]
    
    success_count = 0
    total_count = len(packages)
    
    for module, package in packages:
        if test_import(module, package):
            success_count += 1
    
    print("\n" + "=" * 50)
    print(f"检查结果: {success_count}/{total_count} 个包安装成功")
    
    if success_count == total_count:
        print("🎉 所有依赖都已正确安装！")
        print("\n可以运行以下命令启动应用:")
        print("streamlit run app.py")
        return True
    else:
        print("⚠️  有部分依赖未安装，请运行:")
        print("pip install -r requirements.txt")
        return False

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
