# AI单页PPT生成器 - 运行指南

## 📋 项目概述
AI单页PPT生成器是一个基于人工智能的在线工具，能够根据用户提供的图片或文字描述，快速生成专业、美观的单页PPT布局。

## 🛠️ 环境要求
- Python 3.8 或更高版本
- Windows/macOS/Linux 操作系统
- 至少 2GB 可用内存

## 📦 安装步骤

### 1. 克隆或下载项目
确保您已经在 `d:\newProject\aippt` 目录中有所有项目文件。

### 2. 创建虚拟环境（推荐）
```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate
```

### 3. 安装依赖包
```bash
pip install -r requirements.txt
```

### 4. 配置API密钥
编辑 `config.py` 文件，设置您的 DeepSeek API 密钥：

```python
# 在 config.py 中修改这一行
DEEPSEEK_API_KEY = "your_actual_deepseek_api_key_here"
```

**获取 DeepSeek API 密钥：**
1. 访问 [DeepSeek 官网](https://www.deepseek.com/)
2. 注册账号并登录
3. 在控制台中创建 API 密钥
4. 将密钥复制到 `config.py` 文件中

## 🚀 运行应用

### 启动命令
在项目根目录下运行：
```bash
streamlit run app.py
```

### 访问应用
启动成功后，浏览器会自动打开，或者手动访问：
```
http://localhost:8501
```

## 📱 使用方法

### 方式一：图片生成PPT
1. 点击 "📷 图片生成PPT" 标签页
2. 上传图片文件（支持 JPG、PNG、BMP、GIF）
3. 可选：输入自定义标题和内容要点
4. 点击 "🚀 生成PPT" 按钮
5. 等待AI分析和生成
6. 下载生成的PPT文件

### 方式二：文字生成PPT
1. 点击 "✍️ 文字生成PPT" 标签页
2. 输入详细的内容描述
3. 可以使用示例按钮快速填入模板
4. 点击 "🚀 生成PPT" 按钮
5. 等待AI生成内容和布局
6. 下载生成的PPT文件

## 🔧 故障排除

### 常见问题

**1. 模块导入错误**
```bash
# 确保所有依赖都已安装
pip install -r requirements.txt
```

**2. API密钥错误**
- 检查 `config.py` 中的 API 密钥是否正确
- 确保 API 密钥有效且有足够的配额

**3. 图片上传失败**
- 检查图片格式是否支持
- 确保图片文件大小不超过 10MB

**4. PPT生成失败**
- 检查网络连接
- 确保 API 服务正常
- 查看控制台错误信息

**5. 端口被占用**
```bash
# 使用其他端口运行
streamlit run app.py --server.port 8502
```

### 日志查看
如果遇到问题，可以在命令行中查看详细的错误信息。

## 📁 项目结构
```
aippt/
├── app.py              # Streamlit主应用
├── ai_service.py       # AI服务模块
├── ppt_generator.py    # PPT生成器
├── config.py           # 配置文件
├── requirements.txt    # 依赖包列表
├── 运行指南.md         # 本文件
└── readme.md          # 项目说明
```

## 🎯 功能特性
- ✅ 图片内容智能分析
- ✅ 文字描述自动生成PPT
- ✅ 多种布局模板
- ✅ 一键导出PPT文件
- ✅ 响应式Web界面
- ✅ 实时预览功能

## 📞 技术支持
如果您在使用过程中遇到问题，请：
1. 检查本指南的故障排除部分
2. 查看控制台错误信息
3. 确保所有依赖正确安装
4. 验证API配置是否正确

## 🔄 更新说明
- 定期更新依赖包以获得最新功能
- 关注 DeepSeek API 的更新和变化
- 根据需要调整配置参数

---
**祝您使用愉快！** 🎉
